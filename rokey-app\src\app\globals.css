@import "tailwindcss";

:root {
  --background: #fafafa;
  --background-dark: #0f0f0f;
  --foreground: #1a1a1a;
  --foreground-dark: #ffffff;
  --primary: #3b82f6;
  --primary-dark: #2563eb;
  --secondary: #6366f1;
  --accent: #06b6d4;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --surface: #ffffff;
  --surface-dark: #1a1a1a;
  --surface-light: #f8f9fa;
  --surface-light-dark: #262626;
  --border: #e5e7eb;
  --border-dark: #374151;
  --text-primary: #1a1a1a;
  --text-primary-dark: #ffffff;
  --text-secondary: #6b7280;
  --text-secondary-dark: #d1d5db;
  --text-muted: #9ca3af;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--background-dark);
    --foreground: var(--foreground-dark);
    --surface: var(--surface-dark);
    --surface-light: var(--surface-light-dark);
    --border: var(--border-dark);
    --text-primary: var(--text-primary-dark);
    --text-secondary: var(--text-secondary-dark);
  }
}

/* Override with Reference Design Colors */
:root {
  --background: #faf8f5;
  --foreground: #374151;
  --surface: #ffffff;
  --surface-light: #faf8f5;
  --border: #e5e7eb;
  --text-primary: #374151;
  --text-secondary: #6b7280;
}

@theme {
  --color-background: #faf8f5;
  --color-foreground: #374151;
  --font-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
}

/* Custom scrollbar - Light Theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Smooth animations */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease,
              transform 0.2s ease, box-shadow 0.2s ease, opacity 0.2s ease;
}

/* Focus styles */
*:focus {
  outline: none;
}

/* Text wrapping utilities */
.overflow-wrap-anywhere {
  overflow-wrap: anywhere;
}

.word-break-break-all {
  word-break: break-all;
}

/* Ensure chat messages don't overflow - more conservative approach */
.chat-message-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Specific handling for very long URLs or technical terms */
.chat-message-content a,
.chat-message-content .long-text {
  overflow-wrap: anywhere;
  word-break: break-all;
}

/* Activity details text wrapping */
.activity-details {
  word-wrap: break-word;
  overflow-wrap: anywhere;
  word-break: break-word;
  hyphens: auto;
  max-width: 100%;
}

/* Line clamping utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Spinning animation for status indicator */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

*:focus-visible {
  outline: 2px solid #ff6b35;
  outline-offset: 2px;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Smooth hover animations for interactive elements */
.hover-lift {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
  transition: transform 0.2s ease-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-scale:active {
  transform: scale(0.98);
}

/* Smooth button press animation */
.btn-press {
  transition: transform 0.1s ease-out;
}

.btn-press:active {
  transform: scale(0.95);
}

/* Optimistic Loading Styles */
.optimistic-loading-container {
  animation: fadeIn 0.2s ease-out;
}

.optimistic-loading {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.optimistic-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 8px;
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  transform: translateY(-50%);
}

/* Loading state for navigation */
.nav-loading {
  position: relative;
  overflow: hidden;
}

.nav-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #ff6b35, transparent);
  animation: loading-bar 1.5s ease-in-out infinite;
}

@keyframes loading-bar {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Smooth page transitions */
.page-transition {
  transition: opacity 0.15s ease-out, transform 0.15s ease-out;
}

.page-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-10px);
}

/* Modern card styles */
.card-modern {
  background: var(--surface);
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  border-radius: 16px;
}

.card-elevated {
  background: var(--surface);
  border: 1px solid var(--border);
  box-shadow: var(--shadow-lg);
  border-radius: 16px;
}

/* Glass morphism effect - more subtle */
.glass {
  background: rgba(26, 26, 26, 0.7);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

/* Gradient backgrounds - more subtle */
.gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
}

.gradient-surface {
  background: linear-gradient(135deg, #1a1a1a 0%, #262626 100%);
}

.gradient-subtle {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
}

/* Button hover effects - more subtle */
.btn-hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

/* Card hover effects - more subtle */
.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  border-color: rgba(255, 255, 255, 0.12);
}

/* Modern button styles */
.btn-modern {
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-modern:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Temperature slider styles */
.slider-orange {
  background: #e5e7eb;
}

.slider-orange::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #ff6b35;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.slider-orange::-webkit-slider-thumb:hover {
  background: #e55a2b;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.slider-orange::-webkit-slider-track {
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(to right, #e5e7eb 0%, #ff6b35 50%, #e5e7eb 100%);
}

.slider-orange::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #ff6b35;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.slider-orange::-moz-range-track {
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(to right, #e5e7eb 0%, #ff6b35 50%, #e5e7eb 100%);
}

/* Flowing current animation for connection lines */
@keyframes current-flow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(200%);
    opacity: 0;
  }
}

.current-flow {
  animation: current-flow 2s ease-in-out infinite;
}

.current-flow-delayed {
  animation: current-flow 2s ease-in-out infinite;
  animation-delay: 0.5s;
}

/* Premium role card animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

@keyframes grid-move {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(10px, 10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 107, 53, 0.6);
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom gradient utilities */
.bg-gradient-radial {
  background: radial-gradient(ellipse at center, var(--tw-gradient-stops));
}

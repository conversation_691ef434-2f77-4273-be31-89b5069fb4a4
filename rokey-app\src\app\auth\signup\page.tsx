'use client';

import { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { EyeIcon, EyeSlashIcon, CheckIcon } from '@heroicons/react/24/outline';
import EnhancedGridBackground from '@/components/landing/EnhancedGridBackground';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { useRouter, useSearchParams } from 'next/navigation';

function SignUpPageContent() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createSupabaseBrowserClient();

  // Note: Removed automatic session clearing to avoid conflicts

  // Get selected plan from URL params
  const selectedPlan = searchParams.get('plan');

  useEffect(() => {
    // Redirect to pricing if no plan is selected
    if (!selectedPlan) {
      router.push('/pricing');
      return;
    }

    // Validate plan
    if (!['free', 'starter', 'professional', 'enterprise'].includes(selectedPlan)) {
      router.push('/pricing');
      return;
    }

    // TEMPORARILY DISABLED: Check if user is already signed in
    // This is causing redirect loops, so we'll let users manually fill out the form
    console.log('Signup page loaded, automatic session check disabled to prevent redirect loops');

    /*
    const checkUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        console.log('User already signed in, checking subscription status...');

        // Check if user has active subscription
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('subscription_status')
          .eq('user_id', session.user.id)
          .single();

        if (profile && profile.subscription_status === 'active') {
          // User has active subscription, go to dashboard
          router.push('/dashboard');
        } else {
          // User needs to complete payment
          router.push(`/checkout?plan=${selectedPlan}&user_id=${session.user.id}`);
        }
      }
    };
    checkUser();
    */
  }, [router, selectedPlan, supabase]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Validation
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setIsLoading(false);
      return;
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      setIsLoading(false);
      return;
    }

    if (!agreedToTerms) {
      setError('Please agree to the Terms of Service and Privacy Policy');
      setIsLoading(false);
      return;
    }

    try {
      // Handle Free Tier vs Paid Tiers differently
      if (selectedPlan === 'free') {
        console.log('Creating free tier user account...');

        // For free tier, use the dedicated free signup API
        const response = await fetch('/api/auth/free-signup', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: formData.email,
            password: formData.password,
            fullName: `${formData.firstName} ${formData.lastName}`
          })
        });

        const result = await response.json();

        if (!response.ok) {
          setError(result.error || 'Failed to create free account');
          return;
        }

        // Sign in the user after successful account creation
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: formData.email,
          password: formData.password,
        });

        if (signInError) {
          setError('Account created but failed to sign in. Please try signing in manually.');
          router.push('/auth/signin');
          return;
        }

        // Redirect to dashboard for free tier users
        console.log('Free tier user created and signed in, redirecting to dashboard...');
        router.push('/dashboard');
        return;
      }

      // For paid tiers, create user account and mark as payment_pending
      console.log('Creating paid tier user account with payment_pending status...');

      const { data, error } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            first_name: formData.firstName,
            last_name: formData.lastName,
            full_name: `${formData.firstName} ${formData.lastName}`,
            plan: selectedPlan || 'professional',
            payment_status: 'pending', // Mark as pending payment
          }
        }
      });

      if (error) {
        if (error.message.includes('already registered')) {
          // Check if this is a pending user who can retry
          console.log('Email already registered, checking if user has pending payment...');

          try {
            // Try to sign in to check user status
            const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
              email: formData.email,
              password: formData.password,
            });

            if (signInData.user && signInData.user.user_metadata?.payment_status === 'pending') {
              console.log('Found pending user, checking if plan needs to be updated...');

              const currentPlan = signInData.user.user_metadata?.plan;
              const newPlan = selectedPlan || 'professional';

              // Update user metadata if plan has changed
              if (currentPlan !== newPlan) {
                console.log(`Updating user plan from ${currentPlan} to ${newPlan}`);

                const { error: updateError } = await supabase.auth.updateUser({
                  data: {
                    ...signInData.user.user_metadata,
                    plan: newPlan,
                    payment_status: 'pending' // Keep as pending
                  }
                });

                if (updateError) {
                  console.error('Failed to update user plan:', updateError);
                  setError('Failed to update plan selection. Please try again.');
                  return;
                }

                console.log('User plan updated successfully');
              }

              // Debug logging
              await fetch('/api/debug/checkout', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  action: 'PENDING_USER_RETRY',
                  userId: signInData.user.id,
                  email: formData.email,
                  selectedPlan: newPlan,
                  previousPlan: currentPlan,
                  planUpdated: currentPlan !== newPlan,
                  message: 'Allowing pending user to retry checkout - user is now signed in',
                  sessionExists: !!signInData.session
                })
              }).catch(() => {});

              // Verify session was established with multiple attempts
              let verifySession = null;
              let attempts = 0;
              const maxAttempts = 3;

              while (!verifySession && attempts < maxAttempts) {
                const { data: { user } } = await supabase.auth.getUser();
                verifySession = user ? { user } : null; // Create session-like object for compatibility
                attempts++;

                if (!verifySession && attempts < maxAttempts) {
                  console.log(`User verification attempt ${attempts}/${maxAttempts} failed, retrying...`);
                  await new Promise(resolve => setTimeout(resolve, 1000));
                }
              }

              console.log('User verification after sign in:', {
                hasUser: !!verifySession,
                userId: verifySession?.user?.id,
                attempts: attempts
              });

              if (verifySession && verifySession.user) {
                console.log('Session verified, redirecting to checkout...');
                // Use router.push instead of window.location.href to maintain session
                router.push(`/checkout?plan=${selectedPlan}&user_id=${verifySession.user.id}&email=${encodeURIComponent(formData.email)}`);
              } else {
                console.error('Session not established after sign in, redirecting to signin page');
                setError('Failed to establish session. Please try signing in manually.');
                router.push(`/auth/signin?plan=${selectedPlan}&email=${encodeURIComponent(formData.email)}&message=session_failed`);
              }
              return;
            } else {
              console.log('Found active user, redirecting to sign in...');
              // Redirect to sign in page for active users
              window.location.href = `/auth/signin?plan=${selectedPlan}&message=account_exists`;
              return;
            }
          } catch (retryError) {
            console.error('Error checking pending user:', retryError);
            // If we can't determine status, redirect to sign in
            window.location.href = `/auth/signin?plan=${selectedPlan}&message=account_exists`;
            return;
          }
        } else {
          setError(error.message);
        }
        return;
      }

      if (!data.user) {
        setError('Failed to create account. Please try again.');
        return;
      }

      // Debug logging to terminal
      await fetch('/api/debug/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'USER_CREATED_PAYMENT_PENDING',
          userId: data.user.id,
          email: formData.email,
          selectedPlan,
          redirectUrl: `/checkout?plan=${selectedPlan}&user_id=${data.user.id}&email=${encodeURIComponent(formData.email)}`,
          paymentStatus: 'pending',
          message: 'User created successfully, user should be automatically signed in'
        })
      }).catch(() => {});

      console.log('User created successfully, checking session...');
      console.log('User ID:', data.user.id);
      console.log('Plan selected:', selectedPlan);

      // Check if user is automatically signed in after signup
      const { data: { user: signedInUser } } = await supabase.auth.getUser();

      if (signedInUser) {
        console.log('User is signed in after signup, redirecting to checkout...');
        // User is signed in, wait a moment for auth to propagate then redirect
        setTimeout(() => {
          window.location.href = `/checkout?plan=${selectedPlan}&user_id=${signedInUser.id}&email=${encodeURIComponent(formData.email)}`;
        }, 500);
      } else {
        console.log('User not automatically signed in, redirecting to signin...');
        // Fallback: redirect to sign in page
        window.location.href = `/auth/signin?plan=${selectedPlan}&checkout_user_id=${data.user.id}&email=${encodeURIComponent(formData.email)}&message=account_created`;
      }

    } catch (err: any) {
      console.error('Sign up error:', err);
      setError(err.message || 'Failed to process signup. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };



  const passwordRequirements = [
    { text: 'At least 8 characters', met: formData.password.length >= 8 },
    { text: 'Contains uppercase letter', met: /[A-Z]/.test(formData.password) },
    { text: 'Contains lowercase letter', met: /[a-z]/.test(formData.password) },
    { text: 'Contains number', met: /\d/.test(formData.password) }
  ];

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Dark Branding */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-[#040716] to-[#1C051C] relative overflow-hidden">
        {/* Enhanced Grid Background */}
        <EnhancedGridBackground
          gridSize={45}
          opacity={0.06}
          color="#ff6b35"
          variant="premium"
          animated={true}
          className="absolute inset-0"
        />

        {/* Center Gradient */}
        <div className="absolute inset-0 bg-gradient-radial from-[#1C051C]/30 via-transparent to-transparent"></div>

        {/* Decorative Elements */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-[#ff6b35]/20 to-[#f7931e]/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-24 h-24 bg-gradient-to-r from-[#f7931e]/20 to-[#ff6b35]/20 rounded-full blur-2xl"></div>

        <div className="relative z-10 flex flex-col justify-center px-12 py-20">
          {/* Logo */}
          <div className="mb-12">
            <div className="flex items-center space-x-3 mb-8">
              <div className="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center border border-white/20">
                <Image
                  src="/roukey_logo.png"
                  alt="RouKey"
                  width={32}
                  height={32}
                  className="w-8 h-8 object-contain"
                  priority
                />
              </div>
              <span className="text-2xl font-bold text-white">RouKey</span>
            </div>
          </div>

          {/* Welcome Message */}
          <div className="mb-12">
            <h1 className="text-4xl font-bold text-white mb-4 leading-tight">
              Join thousands of
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
                developers!
              </span>
            </h1>
            <p className="text-gray-300 text-lg leading-relaxed">
              Start building with unlimited AI requests across 300+ models
            </p>
          </div>

          {/* Sign In Link */}
          <div className="text-gray-300">
            Already have an account?{' '}
            <Link
              href="/auth/signin"
              className="text-[#ff6b35] hover:text-[#f7931e] font-medium transition-colors"
            >
              Sign in
            </Link>
          </div>
        </div>
      </div>

      {/* Right Side - White Form Area */}
      <div className="w-full lg:w-1/2 bg-white flex items-center justify-center p-8">
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="w-full max-w-md"
        >
          {/* Mobile Logo */}
          <div className="lg:hidden text-center mb-8">
            <Link href="/" className="inline-flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center">
                <Image
                  src="/roukey_logo.png"
                  alt="RouKey"
                  width={24}
                  height={24}
                  className="w-6 h-6 object-contain"
                  priority
                />
              </div>
              <span className="text-2xl font-bold text-gray-900">RouKey</span>
            </Link>
          </div>

          {/* Form Header */}
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Create account</h2>
          </div>

          {/* Debug: Manual Sign Out Button */}
          <div className="flex gap-2 mb-4">
            <button
              onClick={async () => {
                await supabase.auth.signOut();
                localStorage.clear();
                window.location.reload();
              }}
              className="text-xs text-gray-400 hover:text-gray-600"
            >
              🔧 Clear Session
            </button>
            <button
              onClick={() => {
                setError('');
                setFormData({
                  firstName: '',
                  lastName: '',
                  email: '',
                  password: '',
                  confirmPassword: ''
                });
              }}
              className="text-xs text-gray-400 hover:text-gray-600"
            >
              🗑️ Clear Form
            </button>
          {/* Selected Plan Display */}
          {selectedPlan && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center justify-center space-x-2">
                <span className="text-blue-600 font-medium text-sm">
                  {selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1)} Plan Selected
                </span>
              </div>
              <p className="text-center text-gray-600 text-xs mt-1">
                {selectedPlan === 'free'
                  ? "You'll be redirected to your dashboard after creating your account"
                  : "You'll be redirected to checkout after creating your account"
                }
              </p>
              <div className="text-center mt-2">
                <Link href="/pricing" className="text-blue-600 hover:text-blue-500 text-xs font-medium transition-colors">
                  Change plan
                </Link>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm">{error}</p>
              {error.includes('already registered') && (
                <div className="mt-3">
                  <Link
                    href="/auth/signin"
                    className="text-blue-600 hover:text-blue-500 font-medium text-sm transition-colors"
                  >
                    → Go to Sign In page
                  </Link>
                </div>
              )}
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Name Fields */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <input
                  id="firstName"
                  name="firstName"
                  type="text"
                  required
                  value={formData.firstName}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 placeholder-gray-500"
                  placeholder="First name"
                />
              </div>
              <div>
                <input
                  id="lastName"
                  name="lastName"
                  type="text"
                  required
                  value={formData.lastName}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 placeholder-gray-500"
                  placeholder="Last name"
                />
              </div>
            </div>

            {/* Email */}
            <div>
                name="email"
                type="email"
                autoComplete="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 placeholder-gray-500"
                placeholder="Email address"
              />
            </div>

            {/* Password */}
            <div>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 placeholder-gray-500"
                  placeholder="Password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5" />
                  ) : (
                    <EyeIcon className="h-5 w-5" />
                  )}
                </button>
              </div>

              {/* Password Requirements */}
              {formData.password && (
                <div className="mt-3 p-3 bg-gray-50 rounded-lg space-y-2">
                  {passwordRequirements.map((req, index) => (
                    <div key={index} className="flex items-center text-sm">
                      <CheckIcon className={`h-4 w-4 mr-2 ${req.met ? 'text-green-500' : 'text-gray-300'}`} />
                      <span className={req.met ? 'text-green-600 font-medium' : 'text-gray-500'}>{req.text}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>

              <div className="relative">
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  required
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 placeholder-gray-500"
                  placeholder="Confirm password"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showConfirmPassword ? (
                    <EyeSlashIcon className="h-5 w-5" />
                  ) : (
                    <EyeIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>

            {/* Terms Agreement */}
            <div className="flex items-start">
              <input
                id="terms"
                name="terms"
                type="checkbox"
                checked={agreedToTerms}
                onChange={(e) => setAgreedToTerms(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
              />
              <label htmlFor="terms" className="ml-2 block text-sm text-gray-600">
                I agree to the{' '}
                <Link href="/terms" className="text-blue-600 hover:text-blue-500 font-medium transition-colors">
                  Terms of Service
                </Link>{' '}
                and{' '}
                <Link href="/privacy" className="text-blue-600 hover:text-blue-500 font-medium transition-colors">
                  Privacy Policy
                </Link>
              </label>
            <button
              type="submit"
              disabled={isLoading || !agreedToTerms}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-500/30 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Creating account...
                </div>
              ) : (
                'Create account'
              )}
            </button>
          </form>

          {/* Mobile Sign In Link */}
          <div className="lg:hidden text-center mt-6">
            <p className="text-gray-600">
              Already have an account?{' '}
              <Link href="/auth/signin" className="text-blue-600 hover:text-blue-500 font-medium transition-colors">
                Sign in
              </Link>
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

export default function SignUpPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <SignUpPageContent />
    </Suspense>
  );
}

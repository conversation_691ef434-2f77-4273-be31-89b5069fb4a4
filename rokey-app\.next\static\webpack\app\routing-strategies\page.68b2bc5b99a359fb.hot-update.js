"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/routing-strategies/page",{

/***/ "(app-pages-browser)/./src/app/routing-strategies/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/routing-strategies/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoutingStrategiesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/InstantLink */ \"(app-pages-browser)/./src/components/ui/InstantLink.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst routingStrategies = [\n    {\n        id: 'intelligent_role',\n        name: \"Smart Role Routing\",\n        shortDescription: 'AI-powered intelligent classification',\n        description: \"RouKey's flagship feature uses advanced AI to analyze requests and automatically route them to the optimal model based on task type and complexity.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        features: [\n            'Intelligent request classification',\n            'Automatic role detection',\n            'Context-aware routing',\n            'Seamless fallback handling'\n        ],\n        useCase: 'Ideal for applications requiring different AI capabilities - coding, writing, analysis, or general chat.',\n        performance: 'Up to 40% better response quality with optimal model selection',\n        complexity: 'Advanced',\n        color: 'from-[#ff6b35] to-[#f7931e]',\n        badge: 'FLAGSHIP'\n    },\n    {\n        id: 'cost_optimization',\n        name: 'Cost-Optimized Routing',\n        shortDescription: 'Minimize costs while maintaining quality',\n        description: \"Smart routing that prioritizes cost-effective models while maintaining response quality through intelligent model selection and caching.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        features: [\n            'Cost-aware model selection',\n            'Semantic caching integration',\n            'Quality threshold maintenance',\n            'Real-time cost tracking'\n        ],\n        useCase: 'Perfect for high-volume applications where cost optimization is critical without sacrificing quality.',\n        performance: 'Up to 70% cost reduction with maintained quality',\n        complexity: 'Intermediate',\n        color: 'from-emerald-500 to-emerald-600',\n        badge: 'POPULAR'\n    },\n    {\n        id: 'load_balancing',\n        name: 'Smart Load Balancing',\n        shortDescription: 'Intelligent distribution across providers',\n        description: \"Advanced load balancing with health monitoring, automatic failover, and intelligent distribution based on provider performance and availability.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        features: [\n            'Health-aware distribution',\n            'Automatic failover',\n            'Performance monitoring',\n            'Zero-downtime switching'\n        ],\n        useCase: 'Essential for production applications requiring maximum uptime and reliability.',\n        performance: 'Excellent reliability with 99.9% uptime guarantee',\n        complexity: 'Beginner',\n        color: 'from-blue-500 to-blue-600'\n    },\n    {\n        id: 'multi_agent',\n        name: 'Multi-Agent Orchestration',\n        shortDescription: 'Coordinate multiple AI agents',\n        description: 'RouKey\\'s advanced orchestration system coordinates multiple specialized AI agents to handle complex workflows requiring different expertise areas.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        features: [\n            'Sequential workflows',\n            'Parallel agent execution',\n            'Supervisor coordination',\n            'Memory persistence'\n        ],\n        useCase: 'Perfect for complex tasks requiring multiple AI specializations like research, analysis, and content creation.',\n        performance: 'Superior results through specialized agent coordination',\n        complexity: 'Expert',\n        color: 'from-purple-500 to-purple-600',\n        badge: 'UNIQUE'\n    },\n    {\n        id: 'performance_routing',\n        name: 'Performance-Optimized Routing',\n        shortDescription: 'Speed and latency optimization',\n        description: 'Intelligent routing that prioritizes response speed and minimizes latency by selecting the fastest available models and providers.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        features: [\n            'Latency-aware routing',\n            'Speed optimization',\n            'Real-time performance monitoring',\n            'Geographic proximity routing'\n        ],\n        useCase: 'Ideal for real-time applications where response speed is critical, such as live chat or interactive systems.',\n        performance: 'Up to 60% faster response times',\n        complexity: 'Intermediate',\n        color: 'from-cyan-500 to-cyan-600'\n    },\n    {\n        id: 'analytics_routing',\n        name: 'Analytics-Driven Routing',\n        shortDescription: 'Data-driven optimization',\n        description: 'Advanced routing that uses historical performance data and analytics to continuously optimize model selection and improve results over time.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        features: [\n            'Historical performance analysis',\n            'Continuous learning optimization',\n            'Quality trend monitoring',\n            'Predictive model selection'\n        ],\n        useCase: 'Best for mature applications with substantial usage data that want to leverage analytics for optimal routing decisions.',\n        performance: 'Continuously improving performance through data insights',\n        complexity: 'Expert',\n        color: 'from-indigo-500 to-indigo-600'\n    }\n];\nfunction RoutingStrategiesPage() {\n    _s();\n    const [selectedStrategy, setSelectedStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(routingStrategies[1]); // Default to intelligent role\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-gray-50 to-white border-b border-gray-200 pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.h1, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"text-4xl md:text-5xl font-bold text-black mb-6\",\n                                children: [\n                                    \"RouKey's Advanced \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-[#ff6b35]\",\n                                        children: \"Routing Strategies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                children: \"Choose the perfect routing strategy for your AI applications. From simple load balancing to advanced multi-role orchestration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/pricing\",\n                                    className: \"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors\",\n                                    children: [\n                                        \"View Pricing Plans\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: routingStrategies.map((strategy, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        onClick: ()=>setSelectedStrategy(strategy),\n                                        className: \"relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 \".concat(selectedStrategy.id === strategy.id ? 'border-[#ff6b35] bg-[#ff6b35]/5' : 'border-gray-200 hover:border-gray-300'),\n                                        children: [\n                                            strategy.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-3 -right-3 bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg bg-gradient-to-r \".concat(strategy.color),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(strategy.icon, {\n                                                            className: \"h-6 w-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-black mb-2\",\n                                                                children: strategy.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm mb-3\",\n                                                                children: strategy.shortDescription\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(strategy.complexity === 'Beginner' ? 'bg-green-100 text-green-800' : strategy.complexity === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'),\n                                                                        children: strategy.complexity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    selectedStrategy.id === strategy.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-[#ff6b35]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                        lineNumber: 217,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, strategy.id, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sticky top-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"bg-white border border-gray-200 rounded-xl p-6 shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-gradient-to-r \".concat(selectedStrategy.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(selectedStrategy.icon, {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-black\",\n                                                            children: selectedStrategy.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: selectedStrategy.shortDescription\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mb-6\",\n                                            children: selectedStrategy.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-2\",\n                                                            children: \"Key Features\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: selectedStrategy.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-[#ff6b35]\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                            lineNumber: 254,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-700 text-sm\",\n                                                                            children: feature\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-2\",\n                                                            children: \"Best Use Case\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 text-sm\",\n                                                            children: selectedStrategy.useCase\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-2\",\n                                                            children: \"Performance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 text-sm\",\n                                                            children: selectedStrategy.performance\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 pt-6 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                href: \"/pricing\",\n                                                className: \"w-full inline-flex items-center justify-center px-4 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Get Started\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, selectedStrategy.id, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                            children: \"Ready to Optimize Your AI Routing?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-white/90 mb-8\",\n                            children: \"Start with any strategy and switch anytime. No configuration required.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/auth/signup?plan=professional\",\n                            className: \"inline-flex items-center px-8 py-4 bg-white text-[#ff6b35] font-bold rounded-xl hover:bg-gray-50 transition-colors text-lg\",\n                            children: [\n                                \"Start Building Now\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"ml-3 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutingStrategiesPage, \"4ayf+QIiw8AtwaO/Cyi5ZAptEMo=\");\n_c = RoutingStrategiesPage;\nvar _c;\n$RefreshReg$(_c, \"RoutingStrategiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/routing-strategies/page.tsx\n"));

/***/ })

});
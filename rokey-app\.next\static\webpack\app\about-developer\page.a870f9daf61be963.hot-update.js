"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about-developer/page",{

/***/ "(app-pages-browser)/./src/components/landing/LandingNavbar.tsx":
/*!**************************************************!*\
  !*** ./src/components/landing/LandingNavbar.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/InstantLink */ \"(app-pages-browser)/./src/components/ui/InstantLink.tsx\");\n/* harmony import */ var _hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useInstantNavigation */ \"(app-pages-browser)/./src/hooks/useInstantNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LandingNavbar() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [dropdownOpen, setDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastScrollY, setLastScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const toggleDropdown = (dropdown)=>{\n        setDropdownOpen(dropdownOpen === dropdown ? null : dropdown);\n    };\n    const handleSignOut = async ()=>{\n        try {\n            const { createSupabaseBrowserClient } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_client_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\"));\n            const supabase = createSupabaseBrowserClient();\n            await supabase.auth.signOut();\n            setIsAuthenticated(false);\n        } catch (error) {\n            console.error('Sign out error:', error);\n        }\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingNavbar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"LandingNavbar.useEffect.handleClickOutside\": ()=>{\n                    setDropdownOpen(null);\n                }\n            }[\"LandingNavbar.useEffect.handleClickOutside\"];\n            if (dropdownOpen) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"LandingNavbar.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"LandingNavbar.useEffect\"];\n            }\n        }\n    }[\"LandingNavbar.useEffect\"], [\n        dropdownOpen\n    ]);\n    // Initialize instant navigation\n    (0,_hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__.useInstantNavigation)();\n    // Check authentication state specifically for landing page\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingNavbar.useEffect\": ()=>{\n            const checkAuth = {\n                \"LandingNavbar.useEffect.checkAuth\": async ()=>{\n                    try {\n                        const { createSupabaseBrowserClient } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_client_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\"));\n                        const supabase = createSupabaseBrowserClient();\n                        const { data: { user } } = await supabase.auth.getUser();\n                        console.log('Landing navbar auth check:', !!user);\n                        setIsAuthenticated(!!user);\n                    } catch (error) {\n                        console.error('Landing navbar auth error:', error);\n                        setIsAuthenticated(false);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"LandingNavbar.useEffect.checkAuth\"];\n            checkAuth();\n            // Listen for auth changes\n            const setupAuthListener = {\n                \"LandingNavbar.useEffect.setupAuthListener\": async ()=>{\n                    try {\n                        const { createSupabaseBrowserClient } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_client_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\"));\n                        const supabase = createSupabaseBrowserClient();\n                        const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                            \"LandingNavbar.useEffect.setupAuthListener\": (event, session)=>{\n                                console.log('Landing navbar auth change:', event, !!session);\n                                setIsAuthenticated(!!(session === null || session === void 0 ? void 0 : session.user));\n                            }\n                        }[\"LandingNavbar.useEffect.setupAuthListener\"]);\n                        return ({\n                            \"LandingNavbar.useEffect.setupAuthListener\": ()=>subscription.unsubscribe()\n                        })[\"LandingNavbar.useEffect.setupAuthListener\"];\n                    } catch (error) {\n                        console.error('Landing navbar auth listener error:', error);\n                    }\n                }\n            }[\"LandingNavbar.useEffect.setupAuthListener\"];\n            const cleanup = setupAuthListener();\n            return ({\n                \"LandingNavbar.useEffect\": ()=>{\n                    cleanup === null || cleanup === void 0 ? void 0 : cleanup.then({\n                        \"LandingNavbar.useEffect\": (fn)=>fn === null || fn === void 0 ? void 0 : fn()\n                    }[\"LandingNavbar.useEffect\"]);\n                }\n            })[\"LandingNavbar.useEffect\"];\n        }\n    }[\"LandingNavbar.useEffect\"], []);\n    // Handle scroll-based navbar visibility\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingNavbar.useEffect\": ()=>{\n            const handleScroll = {\n                \"LandingNavbar.useEffect.handleScroll\": ()=>{\n                    const currentScrollY = window.scrollY;\n                    // Show navbar when at top of page\n                    if (currentScrollY < 10) {\n                        setIsVisible(true);\n                    } else if (currentScrollY > lastScrollY && currentScrollY > 100) {\n                        setIsVisible(false);\n                    } else if (currentScrollY < lastScrollY) {\n                        setIsVisible(true);\n                    }\n                    setLastScrollY(currentScrollY);\n                }\n            }[\"LandingNavbar.useEffect.handleScroll\"];\n            // Throttle scroll events for better performance\n            let ticking = false;\n            const throttledHandleScroll = {\n                \"LandingNavbar.useEffect.throttledHandleScroll\": ()=>{\n                    if (!ticking) {\n                        requestAnimationFrame({\n                            \"LandingNavbar.useEffect.throttledHandleScroll\": ()=>{\n                                handleScroll();\n                                ticking = false;\n                            }\n                        }[\"LandingNavbar.useEffect.throttledHandleScroll\"]);\n                        ticking = true;\n                    }\n                }\n            }[\"LandingNavbar.useEffect.throttledHandleScroll\"];\n            window.addEventListener('scroll', throttledHandleScroll, {\n                passive: true\n            });\n            return ({\n                \"LandingNavbar.useEffect\": ()=>window.removeEventListener('scroll', throttledHandleScroll)\n            })[\"LandingNavbar.useEffect\"];\n        }\n    }[\"LandingNavbar.useEffect\"], [\n        lastScrollY\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.nav, {\n        className: \"fixed top-4 left-0 right-0 z-50 px-6\",\n        initial: {\n            y: 0,\n            opacity: 1\n        },\n        animate: {\n            y: isVisible ? 0 : -100,\n            opacity: isVisible ? 1 : 0\n        },\n        transition: {\n            duration: 0.3,\n            ease: [\n                0.25,\n                0.46,\n                0.45,\n                0.94\n            ] // Custom easing for smooth animation\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between max-w-7xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: \"/RouKey_Logo_NOGLOW.png\",\n                                            alt: \"RouKey\",\n                                            width: 44,\n                                            height: 44,\n                                            className: \"h-11 w-11 transition-opacity duration-200\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: \"/RouKey_Logo_GLOW.png\",\n                                            alt: \"RouKey Glow\",\n                                            width: 44,\n                                            height: 44,\n                                            className: \"h-11 w-11 absolute top-0 left-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"RouKey\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-6 py-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                toggleDropdown('features');\n                                            },\n                                            className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Features\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        dropdownOpen === 'features' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/features\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Product overview\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/integrations\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Integrations\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/templates\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Templates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/ai\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/routing-strategies\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Routing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                toggleDropdown('docs');\n                                            },\n                                            className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Docs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        dropdownOpen === 'docs' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/docs#overview\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Overview\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/docs#features\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Features\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/docs#api-reference\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"API Reference\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/docs#use-cases\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Use Cases\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/docs#faq\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"FAQ\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                toggleDropdown('about');\n                                            },\n                                            className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        dropdownOpen === 'about' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/about-developer\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"About Developer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/about\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"About RouKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/contact\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/pricing\",\n                                    className: \"text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                    children: \"Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: !loading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/dashboard\",\n                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/auth/signin\",\n                                        className: \"text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"relative inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 group overflow-hidden bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-400 hover:to-red-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 font-semibold\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-t from-transparent via-white/10 to-white/20 group-hover:via-white/20 group-hover:to-white/30 transition-all duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"text-gray-300 hover:text-white transition-colors\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -10\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                exit: {\n                    opacity: 0,\n                    y: -10\n                },\n                className: \"md:hidden mt-4 bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl mx-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col space-y-4 px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/features\",\n                            className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                            children: \"Features\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/docs\",\n                            className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                            children: \"Docs\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/about\",\n                            className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                            children: \"About\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/pricing\",\n                            className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                            children: \"Pricing\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, this),\n                        !loading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/dashboard\",\n                            className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold text-center\",\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/auth/signin\",\n                                    className: \"text-gray-300 hover:text-white transition-colors text-center\",\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/pricing\",\n                                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 text-white px-6 py-2 rounded-lg text-center font-medium\",\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 17\n                        }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                lineNumber: 324,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingNavbar, \"5MkmPX0vO2SJt+oGY47ri83yzLY=\", false, function() {\n    return [\n        _hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__.useInstantNavigation\n    ];\n});\n_c = LandingNavbar;\nvar _c;\n$RefreshReg$(_c, \"LandingNavbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\n"));

/***/ })

});
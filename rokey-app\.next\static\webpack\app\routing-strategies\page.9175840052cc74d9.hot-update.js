"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/routing-strategies/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ChartBarIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z\"\n    }));\n}\n_c = ChartBarIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ChartBarIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChartBarIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ClockIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n    }));\n}\n_c = ClockIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ClockIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ClockIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction UserGroupIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z\"\n    }));\n}\n_c = UserGroupIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(UserGroupIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"UserGroupIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL1VzZXJHcm91cEljb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0I7QUFDL0IsU0FBU0MsY0FBYyxLQUl0QixFQUFFQyxNQUFNO1FBSmMsRUFDckJDLEtBQUssRUFDTEMsT0FBTyxFQUNQLEdBQUdDLE9BQ0osR0FKc0I7SUFLckIsT0FBTyxXQUFXLEdBQUVMLGdEQUFtQixDQUFDLE9BQU9PLE9BQU9DLE1BQU0sQ0FBQztRQUMzREMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsYUFBYTtRQUNiQyxRQUFRO1FBQ1IsZUFBZTtRQUNmLGFBQWE7UUFDYkMsS0FBS1o7UUFDTCxtQkFBbUJFO0lBQ3JCLEdBQUdDLFFBQVFGLFFBQVEsV0FBVyxHQUFFSCxnREFBbUIsQ0FBQyxTQUFTO1FBQzNEZSxJQUFJWDtJQUNOLEdBQUdELFNBQVMsTUFBTSxXQUFXLEdBQUVILGdEQUFtQixDQUFDLFFBQVE7UUFDekRnQixlQUFlO1FBQ2ZDLGdCQUFnQjtRQUNoQkMsR0FBRztJQUNMO0FBQ0Y7S0F0QlNqQjtBQXVCVCxNQUFNa0IsYUFBYSxXQUFXLEdBQUduQiw2Q0FBZ0IsQ0FBQ0M7O0FBQ2xELGlFQUFla0IsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXEBoZXJvaWNvbnNcXHJlYWN0XFwyNFxcb3V0bGluZVxcZXNtXFxVc2VyR3JvdXBJY29uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gVXNlckdyb3VwSWNvbih7XG4gIHRpdGxlLFxuICB0aXRsZUlkLFxuICAuLi5wcm9wc1xufSwgc3ZnUmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCBPYmplY3QuYXNzaWduKHtcbiAgICB4bWxuczogXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiLFxuICAgIGZpbGw6IFwibm9uZVwiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgc3Ryb2tlV2lkdGg6IDEuNSxcbiAgICBzdHJva2U6IFwiY3VycmVudENvbG9yXCIsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIixcbiAgICBcImRhdGEtc2xvdFwiOiBcImljb25cIixcbiAgICByZWY6IHN2Z1JlZixcbiAgICBcImFyaWEtbGFiZWxsZWRieVwiOiB0aXRsZUlkXG4gIH0sIHByb3BzKSwgdGl0bGUgPyAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInRpdGxlXCIsIHtcbiAgICBpZDogdGl0bGVJZFxuICB9LCB0aXRsZSkgOiBudWxsLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiLFxuICAgIGQ6IFwiTTE4IDE4LjcyYTkuMDk0IDkuMDk0IDAgMCAwIDMuNzQxLS40NzkgMyAzIDAgMCAwLTQuNjgyLTIuNzJtLjk0IDMuMTk4LjAwMS4wMzFjMCAuMjI1LS4wMTIuNDQ3LS4wMzcuNjY2QTExLjk0NCAxMS45NDQgMCAwIDEgMTIgMjFjLTIuMTcgMC00LjIwNy0uNTc2LTUuOTYzLTEuNTg0QTYuMDYyIDYuMDYyIDAgMCAxIDYgMTguNzE5bTEyIDBhNS45NzEgNS45NzEgMCAwIDAtLjk0MS0zLjE5N20wIDBBNS45OTUgNS45OTUgMCAwIDAgMTIgMTIuNzVhNS45OTUgNS45OTUgMCAwIDAtNS4wNTggMi43NzJtMCAwYTMgMyAwIDAgMC00LjY4MSAyLjcyIDguOTg2IDguOTg2IDAgMCAwIDMuNzQuNDc3bS45NC0zLjE5N2E1Ljk3MSA1Ljk3MSAwIDAgMC0uOTQgMy4xOTdNMTUgNi43NWEzIDMgMCAxIDEtNiAwIDMgMyAwIDAgMSA2IDBabTYgM2EyLjI1IDIuMjUgMCAxIDEtNC41IDAgMi4yNSAyLjI1IDAgMCAxIDQuNSAwWm0tMTMuNSAwYTIuMjUgMi4yNSAwIDEgMS00LjUgMCAyLjI1IDIuMjUgMCAwIDEgNC41IDBaXCJcbiAgfSkpO1xufVxuY29uc3QgRm9yd2FyZFJlZiA9IC8qI19fUFVSRV9fKi8gUmVhY3QuZm9yd2FyZFJlZihVc2VyR3JvdXBJY29uKTtcbmV4cG9ydCBkZWZhdWx0IEZvcndhcmRSZWY7Il0sIm5hbWVzIjpbIlJlYWN0IiwiVXNlckdyb3VwSWNvbiIsInN2Z1JlZiIsInRpdGxlIiwidGl0bGVJZCIsInByb3BzIiwiY3JlYXRlRWxlbWVudCIsIk9iamVjdCIsImFzc2lnbiIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJzdHJva2VXaWR0aCIsInN0cm9rZSIsInJlZiIsImlkIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwiZCIsIkZvcndhcmRSZWYiLCJmb3J3YXJkUmVmIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/routing-strategies/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/routing-strategies/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoutingStrategiesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,ChartBarIcon,CheckIcon,ClockIcon,Cog6ToothIcon,CurrencyDollarIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/InstantLink */ \"(app-pages-browser)/./src/components/ui/InstantLink.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst routingStrategies = [\n    {\n        id: 'intelligent_role',\n        name: \"Smart Role Routing\",\n        shortDescription: 'AI-powered intelligent classification',\n        description: \"RouKey's flagship feature uses advanced AI to analyze requests and automatically route them to the optimal model based on task type and complexity.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        features: [\n            'Intelligent request classification',\n            'Automatic role detection',\n            'Context-aware routing',\n            'Seamless fallback handling'\n        ],\n        useCase: 'Ideal for applications requiring different AI capabilities - coding, writing, analysis, or general chat.',\n        performance: 'Up to 40% better response quality with optimal model selection',\n        complexity: 'Advanced',\n        color: 'from-[#ff6b35] to-[#f7931e]',\n        badge: 'FLAGSHIP'\n    },\n    {\n        id: 'cost_optimization',\n        name: 'Cost-Optimized Routing',\n        shortDescription: 'Minimize costs while maintaining quality',\n        description: \"Smart routing that prioritizes cost-effective models while maintaining response quality through intelligent model selection and caching.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        features: [\n            'Cost-aware model selection',\n            'Semantic caching integration',\n            'Quality threshold maintenance',\n            'Real-time cost tracking'\n        ],\n        useCase: 'Perfect for high-volume applications where cost optimization is critical without sacrificing quality.',\n        performance: 'Up to 70% cost reduction with maintained quality',\n        complexity: 'Intermediate',\n        color: 'from-emerald-500 to-emerald-600',\n        badge: 'POPULAR'\n    },\n    {\n        id: 'load_balancing',\n        name: 'Smart Load Balancing',\n        shortDescription: 'Intelligent distribution across providers',\n        description: \"Advanced load balancing with health monitoring, automatic failover, and intelligent distribution based on provider performance and availability.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        features: [\n            'Health-aware distribution',\n            'Automatic failover',\n            'Performance monitoring',\n            'Zero-downtime switching'\n        ],\n        useCase: 'Essential for production applications requiring maximum uptime and reliability.',\n        performance: 'Excellent reliability with 99.9% uptime guarantee',\n        complexity: 'Beginner',\n        color: 'from-blue-500 to-blue-600'\n    },\n    {\n        id: 'multi_agent',\n        name: 'Multi-Agent Orchestration',\n        shortDescription: 'Coordinate multiple AI agents',\n        description: 'RouKey\\'s advanced orchestration system coordinates multiple specialized AI agents to handle complex workflows requiring different expertise areas.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        features: [\n            'Sequential workflows',\n            'Parallel agent execution',\n            'Supervisor coordination',\n            'Memory persistence'\n        ],\n        useCase: 'Perfect for complex tasks requiring multiple AI specializations like research, analysis, and content creation.',\n        performance: 'Superior results through specialized agent coordination',\n        complexity: 'Expert',\n        color: 'from-purple-500 to-purple-600',\n        badge: 'UNIQUE'\n    },\n    {\n        id: 'performance_routing',\n        name: 'Performance-Optimized Routing',\n        shortDescription: 'Speed and latency optimization',\n        description: 'Intelligent routing that prioritizes response speed and minimizes latency by selecting the fastest available models and providers.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        features: [\n            'Latency-aware routing',\n            'Speed optimization',\n            'Real-time performance monitoring',\n            'Geographic proximity routing'\n        ],\n        useCase: 'Ideal for real-time applications where response speed is critical, such as live chat or interactive systems.',\n        performance: 'Up to 60% faster response times',\n        complexity: 'Intermediate',\n        color: 'from-cyan-500 to-cyan-600'\n    },\n    {\n        id: 'analytics_routing',\n        name: 'Analytics-Driven Routing',\n        shortDescription: 'Data-driven optimization',\n        description: 'Advanced routing that uses historical performance data and analytics to continuously optimize model selection and improve results over time.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        features: [\n            \"RouKey's learning algorithms\",\n            'Dynamic cost optimization',\n            'Quality preservation',\n            'Automatic model selection'\n        ],\n        useCase: 'Essential for production applications where cost control is critical but quality cannot be compromised.',\n        performance: 'Maximum cost savings with quality assurance',\n        complexity: 'Advanced',\n        color: 'from-emerald-500 to-emerald-600',\n        featured: true\n    },\n    {\n        id: 'ab_routing',\n        name: \"RouKey's A/B Testing Router\",\n        shortDescription: 'Continuous model optimization',\n        description: 'RouKey continuously tests different models with 15% of requests to find the best performing models for your specific use cases. Automatically optimizes routing based on quality and cost metrics.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        features: [\n            'Continuous optimization',\n            'Data-driven decisions',\n            'Performance tracking',\n            'Automatic improvements'\n        ],\n        useCase: 'Perfect for applications that want to continuously improve performance and find the best models for their specific use cases.',\n        performance: 'Self-improving performance over time',\n        complexity: 'Advanced',\n        color: 'from-purple-500 to-purple-600'\n    }\n];\nfunction RoutingStrategiesPage() {\n    _s();\n    const [selectedStrategy, setSelectedStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(routingStrategies[1]); // Default to intelligent role\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-gray-50 to-white border-b border-gray-200 pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.h1, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"text-4xl md:text-5xl font-bold text-black mb-6\",\n                                children: [\n                                    \"RouKey's Advanced \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-[#ff6b35]\",\n                                        children: \"Routing Strategies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                children: \"Choose the perfect routing strategy for your AI applications. From simple load balancing to advanced multi-role orchestration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/pricing\",\n                                    className: \"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors\",\n                                    children: [\n                                        \"View Pricing Plans\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: routingStrategies.map((strategy, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        onClick: ()=>setSelectedStrategy(strategy),\n                                        className: \"relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 \".concat(selectedStrategy.id === strategy.id ? 'border-[#ff6b35] bg-[#ff6b35]/5' : 'border-gray-200 hover:border-gray-300'),\n                                        children: [\n                                            strategy.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-3 -right-3 bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg bg-gradient-to-r \".concat(strategy.color),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(strategy.icon, {\n                                                            className: \"h-6 w-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-black mb-2\",\n                                                                children: strategy.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm mb-3\",\n                                                                children: strategy.shortDescription\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(strategy.complexity === 'Beginner' ? 'bg-green-100 text-green-800' : strategy.complexity === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'),\n                                                                        children: strategy.complexity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    selectedStrategy.id === strategy.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-[#ff6b35]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                        lineNumber: 235,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, strategy.id, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sticky top-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"bg-white border border-gray-200 rounded-xl p-6 shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-gradient-to-r \".concat(selectedStrategy.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(selectedStrategy.icon, {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-black\",\n                                                            children: selectedStrategy.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: selectedStrategy.shortDescription\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mb-6\",\n                                            children: selectedStrategy.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-2\",\n                                                            children: \"Key Features\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: selectedStrategy.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-[#ff6b35]\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                            lineNumber: 272,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-700 text-sm\",\n                                                                            children: feature\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-2\",\n                                                            children: \"Best Use Case\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 text-sm\",\n                                                            children: selectedStrategy.useCase\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-2\",\n                                                            children: \"Performance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 text-sm\",\n                                                            children: selectedStrategy.performance\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 pt-6 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                href: \"/pricing\",\n                                                className: \"w-full inline-flex items-center justify-center px-4 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Get Started\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, selectedStrategy.id, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                            children: \"Ready to Optimize Your AI Routing?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-white/90 mb-8\",\n                            children: \"Start with any strategy and switch anytime. No configuration required.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/auth/signup?plan=professional\",\n                            className: \"inline-flex items-center px-8 py-4 bg-white text-[#ff6b35] font-bold rounded-xl hover:bg-gray-50 transition-colors text-lg\",\n                            children: [\n                                \"Start Building Now\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_ChartBarIcon_CheckIcon_ClockIcon_Cog6ToothIcon_CurrencyDollarIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"ml-3 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutingStrategiesPage, \"4ayf+QIiw8AtwaO/Cyi5ZAptEMo=\");\n_c = RoutingStrategiesPage;\nvar _c;\n$RefreshReg$(_c, \"RoutingStrategiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/routing-strategies/page.tsx\n"));

/***/ })

});